import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { RouteParamsSchema } from '@schemas/common/common';
import { SlugParamSchema } from '@schemas/forum/answer';
import {
  ForumQuestionUpdateLiveSchema,
  ForumQuestionCreateOneSchema,
  ForumQuestionDeleteOneSchema,
  ForumQuestionFetchManySchema,
  ForumQuestionSearchSchema,
  ForumQuestionListingSchema,
} from '@schemas/forum/question';
import type { FastifyInstance, FastifyReply } from 'fastify';

const questionRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/forum/question', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumQuestionCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMQUE010', bodyError);
    }
    const result = await ForumModule.QuestionModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.patch('/backend/api/v1/forum/question-live', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumQuestionUpdateLiveSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMQUE010', bodyError);
    }
    const result = await ForumModule.QuestionModule.updateLive(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  // fastify.patch('/backend/api/v1/forum/question', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
  //   const { data: queryData, error: queryError } = ForumQuestionUpdateOneQuerySchema.safeParse(request.body);
  //   const { data: bodyData, error: bodyError } = ForumQuestionUpdateOneBodySchema.safeParse(request.body);
  //   if (queryError || bodyError) {
  //     throw new AppError('FMQUE009', bodyError);
  //   }
  //   const result = await ForumModule.QuestionModule.updateOne(request, queryData, bodyData);
  //   reply.status(HttpStatus.CREATED).send(result);
  // });
  fastify.delete('/backend/api/v1/forum/question/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ForumQuestionDeleteOneSchema.safeParse(request.params);
    if (queryError) {
      throw new AppError('FMQUE011', queryError);
    }
    await ForumModule.QuestionModule.deleteOne(request, queryData);
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.get('/backend/api/v1/forum/questions', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumQuestionFetchManySchema.safeParse(request.query);

    if (queryError) {
      throw new AppError('FMQUE009', queryError);
    }
    const result = await ForumModule.QuestionModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/forum/questions-search', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumQuestionSearchSchema.safeParse(request.query);

    if (queryError) {
      throw new AppError('FMQUE009', queryError);
    }
    const result = await ForumModule.QuestionModule.search(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/forum/question/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: error, data: data } = RouteParamsSchema.safeParse(request.params);
    if (error) {
      throw new AppError('FMQUE011', error);
    }
    const result = await ForumModule.QuestionModule.fetchOneForClient(request, data);
    reply.status(HttpStatus.OK).send(result);
  });



 
};

export default questionRoutes;
