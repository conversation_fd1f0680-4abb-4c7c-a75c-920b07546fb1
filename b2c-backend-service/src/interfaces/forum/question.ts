import type {
  BooleanNullI,
  DateNullI,
  IdNameTypeI,
  NullableI,
  StringNullI,
  TotalCursorDataI,
} from '@interfaces/common/data';
import type { TopicTransformParamsI } from './topic';
import type { QuestionTypeI } from '@consts/forum/question';
import type { EquipmentCategoryNestedClientDataI } from '@interfaces/ship/equipmentCategory';
import type { EquipmentModelNestedClientI } from '@interfaces/ship/equipmentModel';
import { EquipmentManufacturerNestedClientI } from '@interfaces/ship/equipmentManufacturer';
import { ProfileExternalI, ProfileForDataI } from '@interfaces/user/profile';
import type { ForumAnswerMediaI, ForumAnswerWithProfileForQuestionI } from './answer';
import type { Answer, QuestionMedia } from '@prisma/postgres';
import { VoteTypeI } from '@consts/forum/answer';
export type ForumQuestionFetchManyResultI = {
  id: string;
  title: string;
  description: string;
  slug: string;
  type: QuestionTypeI;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isSolved: boolean;
  isEdited: boolean;
  isAnonymous: boolean;
  canModify: boolean;
  vote: NullableI<VoteTypeI>;
  liveStartedAt: Date;
  isLive: boolean;
  equipmentCategory?: NullableI<EquipmentCategoryNestedClientDataI>;
  equipmentModel?: NullableI<EquipmentModelNestedClientI>;
  equipmentManufacturer?: NullableI<EquipmentManufacturerNestedClientI>;
  topics?: NullableI<IdNameTypeI[]>;
  media: NullableI<Pick<QuestionMedia, 'id' | 'fileExtension' | 'fileUrl'>[]>;
};
export type ProfileShipI = {
  shipImo: StringNullI;
  rawDataImo: StringNullI;
  subVesselTypeId: StringNullI;
  subVesselTypeRawDataId: StringNullI;
  departmentAlternativeId: StringNullI;
  departmentRawDataId: StringNullI;
  equipmentCategoryId: StringNullI;
  equipmentCategoryRawDataId: StringNullI;
  mainVesselTypeId: StringNullI;
};
export type ForumQuestionDetailWithAnswersSQLI = {
  id: string;
  title: string;
  description: StringNullI;
  slug: string;
  type: QuestionTypeI;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isLive: BooleanNullI;
  liveStartedAt: DateNullI;
  isSolved: boolean;
  vote: NullableI<VoteTypeI>;
  equipmentCategoryId: StringNullI;
  equipmentCategoryName: StringNullI;
  equipmentCategoryRawDataId: StringNullI;
  equipmentCategoryRawDataName: StringNullI;
  equipmentModelId: StringNullI;
  equipmentModelName: StringNullI;
  equipmentManufacturerId: StringNullI;
  equipmentManufacturerName: StringNullI;
  createdAt: Date;
  isEdited: boolean;
  profileId: StringNullI;
  profileName: StringNullI;
  profileAvatar: StringNullI;
  topics: IdNameTypeI[] | null;
  media: NullableI<Pick<QuestionMedia, 'id' | 'fileExtension' | 'fileUrl'>[]>;

  answers: NullableI<ForumAnswerWithProfileForQuestionI[]>;
};
export type ForumQuestionMediaI = Pick<QuestionMedia, 'id' | 'fileExtension' | 'fileUrl'>;
export type ForumQuestionDetailWithAnswersI = {
  id: string;
  title: string;
  description: StringNullI;
  slug: string;
  type: QuestionTypeI;
  canModify: boolean;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isLive: BooleanNullI;
  liveStartedAt: DateNullI;
  isSolved: boolean;
  vote: NullableI<VoteTypeI>;
  equipmentCategory: EquipmentCategoryNestedClientDataI;
  equipmentModel: EquipmentModelNestedClientI;
  equipmentManufacturer: EquipmentManufacturerNestedClientI;
  createdAt: Date;
  isEdited: boolean;
  profile: ProfileForDataI;
  topics: NullableI<IdNameTypeI[]>;
  media: NullableI<ForumQuestionMediaI[]>;
  answers: NullableI<ForumAnswerWithProfileForQuestionI[]>;
};

export type ForumQuestionFetchManySQLI = {
  id: string;
  title: string;
  description: string;
  slug: string;
  type: QuestionTypeI;
  profileId: string;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isSolved: boolean;
  isEdited: boolean;
  isAnonymous: boolean;
  liveStartedAt: Date;
  isLive: boolean;
  vote: NullableI<VoteTypeI>;
  equipmentCategoryId?: string;
  equipmentCategoryName?: string;
  equipmentCategoryRawDataId?: string;
  equipmentCategoryRawDataName?: string;
  equipmentModelId?: string;
  equipmentModelName?: string;
  equipmentModelRawDataId?: string;
  equipmentModelRawDataName?: string;
  equipmentManufacturerId?: string;
  equipmentManufacturerName?: string;
  equipmentManufacturerRawDataId?: string;
  equipmentManufacturerRawDataName?: string;
  topics?: TopicTransformParamsI[];
  media: NullableI<ForumQuestionMediaI[]>;
};
export type ForumQuestionSearchItemI = {
  id: string; // UUID (PostgreSQL UUID maps to string)
  title: string; // VARCHAR
  slug: string;
  cursorId: number; // Assuming INT or BIGINT
};
export type ForumQuestionSearchResultI = TotalCursorDataI<ForumQuestionSearchItemI>;
export type TroubleshootQuestionI = {
  id: string;
  title: string;
  description: string;
  slug: string;
  createdAt: Date;
  upvoteCount: number;
  answerCount: number;
  isSolved: boolean;
  community: {
    id: string;
    name: string;
  };
  equipment: {
    category?: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    };
    manufacturer?: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    };
    model?: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    };
  };
};
export type ForumQuestionListingI = {
  id: string;
  title: string;
  content: string;
  isVerified: boolean;
  createdAt: Date;
  topics: IdNameTypeI[];
  media: ForumQuestionMediaI[];
  equipment?: {
    category?: IdNameTypeI;
    manufacturer?: IdNameTypeI;
    model?: IdNameTypeI;
  };
  answerCount: number;
  upvoteCount: number;
  downvoteCount: number;
  verifiedAnswer?: {
    content: string;
    profile: ProfileExternalI;
  };
};
export type FetchForumQuestionListingI = {
  id: string;
  title: string;
  slug: string;
  content: string;
  createdAt: Date;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  isVerified: boolean;
  topics: IdNameTypeI[] | null;
  media: ForumQuestionMediaI[] | null;
  equipment: {
    category?: IdNameTypeI | null;
    manufacturer?: IdNameTypeI | null;
    model?: IdNameTypeI | null;
  } | null;
  verifiedAnswer: {
    content: string;
    profile: ProfileExternalI;
  } | null;
};

export type ForumQuestionDetailWithAnswersTruncatedI = {
  id: string;
  title: string;
  description: StringNullI;
  slug: string;
  type: QuestionTypeI;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isLive: BooleanNullI;
  liveStartedAt: DateNullI;
  isSolved: boolean;
  equipmentCategory: EquipmentCategoryNestedClientDataI;
  equipmentModel: EquipmentModelNestedClientI;
  equipmentManufacturer: EquipmentManufacturerNestedClientI;
  createdAt: Date;
  isEdited: boolean;
  profile: ProfileForDataI;
  topics: NullableI<IdNameTypeI[]>;
  media: NullableI<ForumQuestionMediaI[]>;
  answers: NullableI<ForumAnswerWithProfileForQuestionTruncatedI[]>;
};

export type ForumAnswerWithProfileForQuestionTruncatedI = Pick<
  Answer,
  'id' | 'upvoteCount' | 'downvoteCount' | 'commentCount' | 'status' | 'isEdited' | 'createdAt'
> & { 
  text: string;
  profile: ProfileForDataI; 
  media: NullableI<ForumAnswerMediaI[]>;
};
