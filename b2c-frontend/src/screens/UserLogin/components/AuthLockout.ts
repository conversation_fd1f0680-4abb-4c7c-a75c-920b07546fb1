import AsyncStorage from '@react-native-async-storage/async-storage';

const LOCKOUT_KEY = 'auth_lockout';
const MAX_ATTEMPTS = 10;
const LOCKOUT_DURATION = 10 * 60 * 1000;
export const checkLockout = async (): Promise<{ 
  isLocked: boolean; 
  remainingTime?: number;
  remainingAttempts?: number;
}> => {
  try {
    const lockoutData = await AsyncStorage.getItem(LOCKOUT_KEY);
    
    if (!lockoutData) {
      return { 
        isLocked: false, 
        remainingAttempts: MAX_ATTEMPTS 
      };
    }

    const { timestamp, attempts } = JSON.parse(lockoutData);
    const elapsed = Date.now() - timestamp;
    
    // If lockout period has expired
    if (elapsed >= LOCKOUT_DURATION) {
      await AsyncStorage.removeItem(LOCKOUT_KEY);
      return { 
        isLocked: false, 
        remainingAttempts: MAX_ATTEMPTS 
      };
    }

    // If still in lockout period
    if (attempts >= MAX_ATTEMPTS) {
      return { 
        isLocked: true,
        remainingTime: Math.ceil((LOCKOUT_DURATION - elapsed) / 1000),
        remainingAttempts: 0
      };
    }

    // If not locked but has some attempts recorded
    return { 
      isLocked: false, 
      remainingAttempts: MAX_ATTEMPTS - attempts 
    };

  } catch (error) {
    console.error('Error in checkLockout:', error);
    return { 
      isLocked: false, 
      remainingAttempts: MAX_ATTEMPTS 
    };
  }
};

export const recordFailedAttempt = async (): Promise<{
  isLocked: boolean;
  remainingAttempts: number;
  remainingTime?: number;
}> => {
  try {
    const { isLocked: currentlyLocked } = await checkLockout();
    
    // If already locked, return current status
    if (currentlyLocked) {
      const { remainingTime } = await checkLockout();
      return { 
        isLocked: true, 
        remainingAttempts: 0,
        remainingTime 
      };
    }
try{
const currentDataString = await AsyncStorage.getItem(LOCKOUT_KEY);
}catch(e){
    console.log("error = ",e)
}
    
const currentDataString = await AsyncStorage.getItem(LOCKOUT_KEY);
    let newData;

    if (!currentDataString) {
      newData = { 
        timestamp: Date.now(), 
        attempts: 1 
      };
    } else {
      const parsed = JSON.parse(currentDataString);
      newData = { 
        ...parsed, 
        attempts: parsed.attempts + 1 
      };
    }
    console.log("newData",newData)

    try {
  await AsyncStorage.setItem(LOCKOUT_KEY, JSON.stringify(newData));
} catch (error) {
  console.error('Failed to save lockout:', error);
}

    const isNowLocked = newData.attempts >= MAX_ATTEMPTS;
    
    return {
      isLocked: isNowLocked,
      remainingAttempts: MAX_ATTEMPTS - newData.attempts,
      ...(isNowLocked && { 
        remainingTime: LOCKOUT_DURATION / 1000 
      })
    };

  } catch (error) {
    console.error('Error in recordFailedAttempt:', error);
    return { 
      isLocked: false, 
      remainingAttempts: MAX_ATTEMPTS 
    };
  }
};

export const resetLockout = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(LOCKOUT_KEY);
  } catch (error) {
    console.error('Error in resetLockout:', error);
  }
};