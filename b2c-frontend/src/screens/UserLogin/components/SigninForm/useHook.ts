import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import appleAuth from '@invertase/react-native-apple-authentication';
import {
  GoogleSignin,
  isSuccessResponse,
  type SignInResponse,
} from '@react-native-google-signin/google-signin';
import { jwtDecode } from 'jwt-decode';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import { externalSignInAsync, signInAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { showToast } from '@/src/utilities/toast';
import AppError from '@/src/errors/networks/AppError';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import { useSocket } from '@/src/context/providers/SocketProvider';
import useNotification from '@/src/hooks/notification';
import useStorage from '@/src/hooks/storage';
import type { AuthLoginResultI } from '@/src/networks/auth/types';
import { createAppLogAPI } from '@/src/networks/log/appLog';
import { AppLogPayloadI } from '@/src/networks/log/types';
import type { SignInFormDataI, UseSignInFormI } from './types';
import { checkLockout, recordFailedAttempt, resetLockout } from '../AuthLockout';

GoogleSignin.configure({
  scopes: ['openid', 'profile', 'email'],
  webClientId: Config.WEB_CLIENT_ID,
  iosClientId: Config.IOS_CLIENT_ID,
  profileImageSize: 120,
  offlineAccess: true,
});

const useSignIn = (): UseSignInFormI => {
  const { setStorage } = useStorage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);
  const [isAppleSubmitting, setIsAppleSubmitting] = useState(false);
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const { getDeviceToken } = useNotification();
  const { connectSocket } = useSocket();
  const [isLocked, setIsLocked] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [remainingAttempts, setRemainingAttempts] = useState(10);

  const defaultValues = { email: '', password: '', acceptedTerms: false };
  const methods = useForm<SignInFormDataI>({
    mode: 'onChange',
    defaultValues,
  });

  const navigateBasedOnUserState = async (authResult: AuthLoginResultI) => {
    const {
      isUsernameSaved,
      isPersonalDetailsSaved,
      isWorkDetailsSaved,
      email,
      isEmailVerified,
      profileId,
      isPrivacyPolicyAccepted,
    } = authResult;

    switch (true) {
      case !isEmailVerified:
        navigation.navigate('VerifyEmail', { email, profileId });
        break;

      case !isPrivacyPolicyAccepted:
        navigation.navigate('PolicyAcceptance');
        break;

      case !isUsernameSaved:
        navigation.navigate('SetUsername', { email });
        break;

      case !isPersonalDetailsSaved || !isWorkDetailsSaved:
        navigation.navigate('AddUserDetailScreen');
        break;

      default:
        break;
    }
  };

  useEffect(() => {
  const initLockoutCheck = async () => {
    const { isLocked, remainingTime, remainingAttempts } = await checkLockout();
    setIsLocked(isLocked);
    if (remainingTime) setRemainingTime(remainingTime);
    if (remainingAttempts) setRemainingAttempts(remainingAttempts);
  };
  initLockoutCheck();
}, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isLocked && remainingTime > 0) {
      interval = setInterval(() => {
        setRemainingTime(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isLocked]);

  const onSubmit = async (data: SignInFormDataI) => {
    try {
      setIsSubmitting(true);
      const { email, password } = data;
      const deviceToken = await getDeviceToken();
      const result = await dispatch(
        signInAsync({
          email,
          password,
          ...(deviceToken ? { deviceToken } : {}),
        }),
      ).unwrap();

      if (result.token) {
        await setStorage('token', result.token);
        await setStorage('jwtToken', result.jwtToken);
        await setStorage('userProfileId', result.profileId);
        await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();

        try {
          await connectSocket(result.profileId);
        } catch (_socketError) {
          console.error('Failed to connect socket:', _socketError);
        }
        await resetLockout();
        await navigateBasedOnUserState(result);
      }
    } catch (err: unknown) {
      const errorMessage =
        typeof err === 'string' ? err : (err as any)?.message || 'An error occurred';
      showToast({
        type: 'error',
        message: capitalizeFirstLetter(errorMessage),
        description: 'Please try again later',
      });
      const { isLocked: nowLocked, remainingAttempts, remainingTime } = await recordFailedAttempt();
      setIsLocked(nowLocked);
      setRemainingAttempts(remainingAttempts);
      if (remainingTime) setRemainingTime(remainingTime);
      
      if (nowLocked) {
        setRemainingTime(600); 
      } 
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAppleLogin = async (): Promise<void> => {
    try {
      setIsAppleSubmitting(true);

      if (!appleAuth.isSupported) {
        throw new AppError('Apple Sign-In is not supported on this device');
      }

      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });

      if (!appleAuthRequestResponse?.user || !appleAuthRequestResponse?.identityToken) {
        throw new AppError('Apple sign-in was cancelled or incomplete');
      }

      if (appleAuthRequestResponse.realUserStatus !== appleAuth.UserStatus.LIKELY_REAL) {
        console.warn('User status indicates potential bot or suspicious activity');
      }

      const hasValidAuth =
        appleAuthRequestResponse.identityToken &&
        appleAuthRequestResponse.user &&
        appleAuthRequestResponse.realUserStatus === 1;

      if (hasValidAuth) {
        const deviceToken = await getDeviceToken();
        let userEmail = appleAuthRequestResponse.email;

        if (!userEmail && appleAuthRequestResponse.identityToken) {
          try {
            const decoded = jwtDecode<{ email?: string }>(appleAuthRequestResponse.identityToken);
            userEmail = decoded.email ?? null;
          } catch (jwtError) {
            console.error('JWT decode failed:', jwtError);
          }
        }

        const result = await dispatch(
          externalSignInAsync({
            externalToken: appleAuthRequestResponse.identityToken,
            type: 'APPLE',
            ...(userEmail && userEmail.trim() !== '' ? { email: userEmail } : {}),
            ...(deviceToken ? { deviceToken } : {}),
          }),
        ).unwrap();

        if (result.token) {
          await setStorage('token', result.token);
          await setStorage('jwtToken', result.jwtToken);
          await setStorage('userProfileId', result.profileId);
          await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();

          try {
            await connectSocket(result.profileId);
          } catch (_socketError) {
            console.error('Failed to connect socket:', _socketError);
          }

          await navigateBasedOnUserState(result);
        } else {
          throw new AppError('Invalid response from server');
        }
      }
    } catch (_error: unknown) {
      const errorMessage =
        typeof _error === 'string' ? _error : (_error as Error)?.message || 'Apple sign-in failed';

      const displayMessage = errorMessage.includes('com.apple')
        ? 'Apple signin failed'
        : errorMessage;

      showToast({
        type: 'error',
        message: displayMessage,
        description: 'Please try again later',
      });
    } finally {
      setIsAppleSubmitting(false);
    }
  };

  const handleGoogleLogin = async (): Promise<void> => {
    const appLogPayload: Partial<AppLogPayloadI> = { url: 'google' };
    try {
      setIsGoogleSubmitting(true);
      await GoogleSignin.hasPlayServices();
      const currentGoogleUser = GoogleSignin.getCurrentUser();
      if (currentGoogleUser) await GoogleSignin.signOut();

      const response: SignInResponse = await GoogleSignin.signIn();
      appLogPayload['request'] = JSON.stringify(response);
      appLogPayload['response'] = '';

      if (isSuccessResponse(response) && response.data?.idToken) {
        const deviceToken = await getDeviceToken();
        const result = await dispatch(
          externalSignInAsync({
            externalToken: response.data.idToken,
            type: 'GOOGLE',
            ...(deviceToken ? { deviceToken } : {}),
          }),
        ).unwrap();

        if (result.token) {
          await setStorage('token', result.token);
          await setStorage('jwtToken', result.jwtToken);
          await setStorage('userProfileId', result.profileId);
          await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();

          try {
            await connectSocket(result.profileId);
          } catch (_socketError) {
            console.error('Failed to connect socket:', _socketError);
          }

          await navigateBasedOnUserState(result);
        }
      } else {
        throw new AppError('Google sign-in failed');
      }
    } catch (_error: any) {
      appLogPayload.response = JSON.stringify({ _error, msg: _error?.message });
      const errorMessage =
        typeof _error === 'string' ? _error : _error?.message || 'Google sign-in failed';
      showToast({
        type: 'error',
        message: capitalizeFirstLetter(errorMessage),
        description: 'Please try again later',
      });
    } finally {
      setIsGoogleSubmitting(false);
      try {
        await createAppLogAPI(appLogPayload as AppLogPayloadI);
      } catch (_error) {
        //
      }
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isAppleSubmitting,
    isGoogleSubmitting,
    handleAppleLogin,
    handleGoogleLogin,
    // error
    isLocked,
    remainingTime,
    remainingAttempts
  };
};

export default useSignIn;
