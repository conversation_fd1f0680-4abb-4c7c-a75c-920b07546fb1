export interface SignInPropsI {
  onForgotPassword: () => void;
  onSignUp: () => void;
}

export interface SignInFormDataI {
  email: string;
  password: string;
  acceptedTerms: boolean;
}

export interface UseSignInFormI {
  methods: any;
  isSubmitting: boolean;
  onSubmit: (data: SignInFormDataI) => Promise<void>;
  isAppleSubmitting: boolean;
  isGoogleSubmitting: boolean;
  handleAppleLogin: () => Promise<void>;
  handleGoogleLogin: () => Promise<void>;
  // error: string;
  isLocked:boolean,
    remainingTime:number,
    remainingAttempts:number
}

export interface TermsModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export interface PrivacyModalProps {
  isVisible: boolean;
  onClose: () => void;
}
