import { Platform, Pressable, Text, View } from 'react-native';
import { Image } from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { emailRegex } from '@/src/consts/regEx';
import type { SignInPropsI } from './types';
import useSignIn from './useHook';

const SignInForm = ({ onForgotPassword, onSignUp }: SignInPropsI) => {
  const {
    methods,
    isSubmitting,
    isAppleSubmitting,
    isGoogleSubmitting,
    onSubmit,
    handleAppleLogin,
    handleGoogleLogin,
    // error
    isLocked,
    remainingTime,
    remainingAttempts
  } = useSignIn();

  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
  } = methods;

  return (
    <View className="px-5">
      <View className="my-8">
        <TextView title="Sign in" />
      </View>

      <View>
        <Controller
          control={control}
          name="email"
          rules={{
            required: 'Email is required',
            pattern: {
              value: emailRegex,
              message: 'Invalid email address',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Email ID"
              value={value}
              onChangeText={onChange}
              placeholder="Enter email ID"
              error={errors.email?.message}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          )}
        />
      </View>

      <View className="mt-6">
        <Controller
          control={control}
          name="password"
          rules={{ 
            required: 'Password is required',
            minLength: {
              value: 8,
              message: 'Password must be at least 8 characters long'
            },
            maxLength: {
              value: 32,
              message: 'Password can be at most 32 characters long'
            },
            validate: (value) => {
              if (!/[A-Z]/.test(value)) {
                return 'Password must contain at least one uppercase letter';
              }
              if (!/[a-z]/.test(value)) {
                return 'Password must contain at least one lowercase letter';
              }
              if (!/[0-9]/.test(value)) {
                return 'Password must contain at least one number';
              }
              if (!/[@$!%*?&^#]/.test(value)) {
                return 'Password must contain at least one special character (@, $, !, %, *, ?, &, ^, #)';
              }
              return true;
            }
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Password"
              value={value}
              placeholder="Enter password"
              type="password"
              onChangeText={onChange}
              error={errors.password?.message}
            />
          )}
        />
        {isLocked ? (
        <Text style={{ color: 'red' }}>
          Try again in {Math.floor(remainingTime / 60)}m {remainingTime % 60}s
        </Text>
      ) : (
        <Text style={{ color: 'red' }}>
          {remainingAttempts < 10 && `${remainingAttempts} attempts remaining`}
        </Text>
      )}
        <Pressable onPress={onForgotPassword} className="mt-3">
          <Text className="text-sm text-[#448600] font-medium leading-4">Forgot password</Text>
        </Pressable>
      </View>
      <View className="mt-8">
        <Button
          label="Sign in"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting || isLocked}
          loading={isSubmitting}
          labelClassName="font-medium leading-4 text-base"
        />
        <Button
          label="Sign in with Google"
          onPress={handleGoogleLogin}
          variant={'oauth'}
          disabled={isGoogleSubmitting}
          loading={isGoogleSubmitting}
          labelClassName="text-[#525252] font-medium leading-4 text-base"
          className="mt-3"
          prefixIcon={
            <Image
              source={require('@/src/assets/images/oauth/google.png')}
              className="w-6 h-6"
              resizeMode="contain"
            />
          }
        />

        {Platform.OS === 'ios' ? (
          <Button
            label="Sign in with Apple"
            onPress={handleAppleLogin}
            variant={'oauth'}
            disabled={isAppleSubmitting}
            loading={isAppleSubmitting}
            labelClassName="text-[#525252] font-medium leading-4 text-base"
            className="mt-3"
            prefixIcon={
              <Image
                source={require('@/src/assets/images/oauth/apple.png')}
                className="w-6 h-6"
                resizeMode="contain"
              />
            }
          />
        ) : (
          <></>
        )}

        <View className="flex-row justify-center items-center mt-8">
          <Text className="text-sm text-[#333333]">Don't have an account? </Text>
          <Pressable onPress={onSignUp}>
            <Text className="text-sm text-[#448600] font-medium font-inter-medium">Sign up</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

export default SignInForm;
