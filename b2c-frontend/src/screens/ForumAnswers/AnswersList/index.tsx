/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View, FlatList, Text, ActivityIndicator, RefreshControl } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { selectForumState } from '@/src/redux/selectors/forum';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import ForumPost from '../../Forum/components/ForumPost';
import type { ForumPostProps } from '../../Forum/components/ForumPost/types';
import { getPreviewIconsFromAttachments } from '../../Forum/components/ForumPost/utils';
import Answers from '../Answers';
import type { ForumAnswerProps } from '../Answers/types';
import { useAnswers } from './useHook';

function mapQuestionToForumPostProps(question: ForumQuestionDetailWithAnswersI): ForumPostProps {
  return {
    postId: question.id,
    communityId: '',
    profile: question.profile,
    type: question.type === 'TROUBLESHOOT' ? 'troubleshooting' : 'question',
    topics: question.topics?.map((t) => ({ id: t.id, label: t.name })) ?? [],
    equipment: [
      ...(question.equipmentCategory
        ? [{ id: question.equipmentCategory.id, label: question.equipmentCategory.name }]
        : []),
      ...(question.equipmentManufacturer
        ? [{ id: question.equipmentManufacturer.id, label: question.equipmentManufacturer.name }]
        : []),
      ...(question.equipmentModel
        ? [{ id: question.equipmentModel.id, label: question.equipmentModel.name }]
        : []),
    ],
    title: question.title,
    isSolved: question.isSolved,
    description: question.description ?? '',
    previewIcons: getPreviewIconsFromAttachments(question.media ?? []),
    upVotes: question.upvoteCount,
    downVotes: question.downvoteCount,
    answers: question.answerCount,
    commentCount: question.commentCount,
    endTime: new Date(question.liveStartedAt).getTime() + 24 * 60 * 60 * 1000,
    isLive: question.isLive,
    answerView: true,
    attachments: question.media || [],
    canModify: question.canModify,
    isAnonymous: question.isAnonymous,
  };
}

const AnswersList = ({
  answers,
  question,
}: {
  answers: ForumAnswerProps[];
  question: ForumQuestionDetailWithAnswersI;
}) => {
  const { handleLoadMore, handleRefresh, refreshing, loading, nextCursorId } = useAnswers();
  const { canUpdateStatus } = useSelector(selectForumState);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const postProps = mapQuestionToForumPostProps(question);

  const onRefresh = () => {
    if (question?.id) {
      handleRefresh(question.id);
    }
  };

  return (
    <View className="px-2 flex-1">
      <FlatList
        data={answers}
        keyExtractor={(item) => item.answerId}
        renderItem={({ item }) => {
          return (
            <Answers answer={{ ...item, canUpdateStatus }} question={question} />
          );
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListFooterComponentStyle={{
          marginBottom: 50,
        }}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
        }}
        ListHeaderComponent={
          <View className="bg-white">
            <ForumPost post={postProps} />
            <Text className="text-base font-medium text-black px-5 py-1">
              {question.answers?.length} answers
            </Text>
          </View>
        }
        onEndReached={() => {
          if (nextCursorId) {
            handleLoadMore(question.id);
          }
        }}
        onEndReachedThreshold={0.8}
        ListFooterComponent={
          loading && nextCursorId ? (
            <View className="py-4 items-center">
              <ActivityIndicator size="small" color="#448600" />
            </View>
          ) : (
            <></>
          )
        }
      />
    </View>
  );
};

export default AnswersList;
