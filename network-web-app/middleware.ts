export { auth as middleware } from './auth';

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - manifest.json (PWA manifest)
     * - manifest.webmanifest (PWA manifest)
     * - robots.txt (robots file)
     * - sitemap.xml (sitemap file)
     * - any file with an extension
     */
    '/((?!api|_next/static|_next/image|favicon.ico|manifest.json|manifest.webmanifest|robots.txt|sitemap.xml|.*\\..*).*)',
  ],
};
