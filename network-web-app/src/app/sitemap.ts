import { MetadataRoute } from 'next';
import { fetchQuestionsListingPublicAPI } from '@/networks/forum/globalSearch';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

// Static routes that should always be included
const staticRoutes: MetadataRoute.Sitemap = [
  {
    url: BASE_URL,
    lastModified: new Date(),
    changeFrequency: 'daily',
    priority: 1.0,
  },
  {
    url: `${BASE_URL}/forums`,
    lastModified: new Date(),
    changeFrequency: 'hourly',
    priority: 0.9,
  },
  {
    url: `${BASE_URL}/privacy-policy`,
    lastModified: new Date(),
    changeFrequency: 'yearly',
    priority: 0.3,
  },
  {
    url: `${BASE_URL}/terms`,
    lastModified: new Date(),
    changeFrequency: 'yearly',
    priority: 0.3,
  },
];

async function getQuestionSlugs(): Promise<MetadataRoute.Sitemap> {
  // During build time without API access, return empty array
  if (
    !BASE_URL ||
    (process.env.NODE_ENV === 'production' && !process.env.NEXT_PUBLIC_BASE_URL)
  ) {
    return [];
  }

  try {
    // Fetch questions in batches to get all questions for sitemap
    const questionRoutes: MetadataRoute.Sitemap = [];
    let hasMore = true;
    let cursorDate: Date | undefined = undefined;
    const pageSize = 100; // Fetch in batches of 100

    while (hasMore && questionRoutes.length < 10000) {
      // Limit to 10k questions for performance
      const response = await fetchQuestionsListingPublicAPI({
        pageSize,
        cursorDate,
      });

      if (response.data && response.data.length > 0) {
        // Add each question to the sitemap
        for (const question of response.data) {
          questionRoutes.push({
            url: `${BASE_URL}/forums/question/${question.slug}`,
            lastModified: new Date(question.createdAt),
            changeFrequency: 'weekly',
            priority: 0.7,
          });
        }

        // Check if there are more questions
        if (response.nextCursorDate) {
          cursorDate = response.nextCursorDate;
        } else {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
    }

    return questionRoutes;
  } catch (error) {
    console.error('Error fetching questions for sitemap:', error);
    // Return empty array if there's an error, so static routes still work
    return [];
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // Get dynamic question routes
    const questionRoutes = await getQuestionSlugs();

    // Combine static and dynamic routes
    return [...staticRoutes, ...questionRoutes];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return at least the static routes if dynamic generation fails
    return staticRoutes;
  }
}
