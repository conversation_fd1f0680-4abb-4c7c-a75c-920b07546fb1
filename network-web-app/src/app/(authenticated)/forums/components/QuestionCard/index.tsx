'use client';

import Image from 'next/image';
import Link from 'next/link';
import { QuestionCardPropsI } from './types';
import QuestionButtons from '../QuestionButtons';
import { extractQuestionButtonData } from '../QuestionButtons/utils';
import Question<PERSON><PERSON>Viewer from '../QuestionImageViewer';
import QuestionAttachments from '../QuestionAttachments';

import { QuestionI, ApiQuestionI, MediaFileI } from '@/networks/forum/types';
import { AuthOverlay } from '@/components';

const QuestionCard = ({
  question,
  isAuthenticated = true,
}: QuestionCardPropsI) => {
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const isApiQuestion = (_q: QuestionI | ApiQuestionI): _q is ApiQuestionI => {
    return true;
  };

  // Helper function to get media files
  const getMediaFiles = (): MediaFileI[] => {
    if (isApiQuestion(question)) {
      // Convert QuestionMediaI to MediaFileI format
      return question.media.map(media => ({
        id: media.id,
        url: media.fileUrl,
        name: `file.${media.fileExtension}`,
        type: getMediaType(media.fileExtension),
        mimeType: getMimeType(media.fileExtension),
      }));
    }
    return question.media || [];
  };

  // Helper function to determine media type from extension
  const getMediaType = (extension: string): MediaFileI['type'] => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac'];

    if (imageExts.includes(extension.toLowerCase())) return 'image';
    if (videoExts.includes(extension.toLowerCase())) return 'video';
    if (audioExts.includes(extension.toLowerCase())) return 'audio';
    if (extension.toLowerCase() === 'pdf') return 'pdf';
    if (['doc', 'docx'].includes(extension.toLowerCase())) return 'doc';
    if (['xls', 'xlsx'].includes(extension.toLowerCase())) return 'sheet';
    return 'other';
  };

  // Helper function to get mime type from extension
  const getMimeType = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  };

  // Helper function to get images
  const getImages = (): string[] => {
    if (isApiQuestion(question)) {
      const mediaFiles = getMediaFiles();
      return mediaFiles
        .filter(file => file.type === 'image')
        .map(file => file.url);
    }
    return question.images || [];
  };

  const mediaFiles = getMediaFiles();
  const imageFiles = mediaFiles.filter(file => file.type === 'image');
  const allImages = [...getImages(), ...imageFiles.map(file => file.url)];
  const nonImageFiles = mediaFiles.filter(file => file.type !== 'image');

  // Helper functions for different question types
  const getContent = () => {
    if (isApiQuestion(question)) {
      return question.content;
    }
    return question.content;
  };

  const getTags = () => {
    if (isApiQuestion(question)) {
      const tags: string[] = [];

      // Add topics if available
      if (question.topics && question.topics.length > 0) {
        tags.push(...question.topics.map(topic => topic.name));
      }

      // Add equipment information if available
      if (question.equipment) {
        if (question.equipment.category?.name) {
          tags.push(question.equipment.category.name);
        }
        if (question.equipment.manufacturer?.name) {
          tags.push(question.equipment.manufacturer.name);
        }
        if (question.equipment.model?.name) {
          tags.push(question.equipment.model.name);
        }
      }

      return tags;
    }
    return question.tags || [];
  };

  const getStatus = () => {
    if (isApiQuestion(question)) {
      return question.isVerified ? 'solved' : 'open';
    }
    return question.status || 'open';
  };

  const getAuthorName = () => {
    if (isApiQuestion(question)) {
      // For API questions, we don't have direct author info in the current response
      // This should be added to the API response in the future
      return 'Anonymous User';
    }
    return question.author?.name || '';
  };

  const getVerifiedAnswer = () => {
    if (isApiQuestion(question)) {
      return question.verifiedAnswer;
    }
    return question.topAnswer;
  };

  const getCreatedDate = () => {
    if (isApiQuestion(question)) {
      return question.createdAt
        ? new Date(question.createdAt).toLocaleDateString()
        : '';
    }
    return question.createdAt
      ? new Date(question.createdAt).toLocaleDateString()
      : '';
  };

  const getSlug = () => {
    if (isApiQuestion(question)) {
      return question.slug;
    }
    return question.id; // Fallback to ID for legacy questions
  };

  const content = getContent();
  const tags = getTags();
  const status = getStatus();
  const verifiedAnswer = getVerifiedAnswer();
  const shouldShowTruncated = isApiQuestion(question) && !isAuthenticated;

  return (
    <AuthOverlay
      isAuthenticated={isAuthenticated}
      maxHeight="60%"
      showGradient={true}
      compact={true}
    >
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-1.5 hover:shadow-md transition-shadow duration-200 relative">
        {status === 'solved' && (
          <div className="absolute top-4 right-4 z-10">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
              ✓ Solved
            </span>
          </div>
        )}

        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4 border-gray-100">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#DDEFC8B2] text-[#737373] hover:bg-blue-100 cursor-pointer transition-colors duration-200"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        <div className="flex justify-between items-start">
          <div className="flex-1">
            <Link href={`/forums/question/${getSlug()}`}>
              <h2 className="text-lg font-medium text-gray-900 leading-tight mb-3 hover:text-blue-600 cursor-pointer transition-colors duration-200">
                {question.title}
              </h2>
            </Link>
          </div>
        </div>

        {content && (
          <div className="mb-4 relative">
            <p className="text-gray-700 leading-relaxed">
              {shouldShowTruncated
                ? truncateContent(content, 100)
                : truncateContent(content)}
            </p>
            {/* {shouldShowTruncated && (
            <div className="mt-4">
              <LoginPrompt message="Sign in to read the full question and answers" />
            </div>
          )} */}
          </div>
        )}

        {!shouldShowTruncated && (
          <>
            <QuestionImageViewer allImages={allImages} />
            <QuestionAttachments nonImageFiles={nonImageFiles} />
          </>
        )}

        {status === 'solved' && verifiedAnswer && !shouldShowTruncated && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center mb-2">
              <span className="text-green-600 font-medium text-sm">
                ✓ Verified Answer
              </span>
            </div>
            <div className="flex items-start space-x-3">
              {isApiQuestion(question) &&
              verifiedAnswer &&
              'profile' in verifiedAnswer ? (
                <>
                  {verifiedAnswer.profile.avatar ? (
                    <Image
                      src={verifiedAnswer.profile.avatar}
                      alt={verifiedAnswer.profile.name}
                      width={32}
                      height={32}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-gray-600 text-sm font-medium">
                        {verifiedAnswer.profile.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex flex-col items-start mb-1">
                      <span className="font-medium text-sm text-gray-900">
                        {verifiedAnswer.profile.name}
                      </span>
                      <div>
                        <span className="text-xs text-gray-500">
                          {verifiedAnswer.profile.designation + ' '}
                        </span>
                        {verifiedAnswer.profile.entity && (
                          <span className="text-xs text-gray-500">
                            at {verifiedAnswer.profile.entity}
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {verifiedAnswer.content}
                    </p>
                  </div>
                </>
              ) : (
                verifiedAnswer &&
                'author' in verifiedAnswer && (
                  <>
                    <Image
                      src={verifiedAnswer.author.avatar}
                      alt={verifiedAnswer.author.name}
                      width={32}
                      height={32}
                      className="rounded-full"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm text-gray-900">
                          {verifiedAnswer.author.name}
                        </span>
                        <span className="text-xs text-gray-500">
                          {verifiedAnswer.author.title}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {verifiedAnswer.content}
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-gray-500">
                          {verifiedAnswer.upvotes} upvotes
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(
                            verifiedAnswer.createdAt
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </>
                )
              )}
            </div>
          </div>
        )}

        {/* Stats and Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span className="text-xs">
              by {getAuthorName()} on {getCreatedDate()}
            </span>
          </div>

          {!shouldShowTruncated && (
            <QuestionButtons
              {...extractQuestionButtonData(question)}
              onUpvote={id => console.log('Upvote question:', id)}
              onDownvote={id => console.log('Downvote question:', id)}
              onAnswer={id => console.log('Answer question:', id)}
            />
          )}
        </div>
      </div>
    </AuthOverlay>
  );
};

export default QuestionCard;
