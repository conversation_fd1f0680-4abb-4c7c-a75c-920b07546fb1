import Footer from '@/components/Footer';
import OnBoardHeader from '@/components/OnboardHeader';
import { auth } from '../../../auth';
import { redirect } from 'next/navigation';

export default async function OnBoardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();
  const isAuthenticated = !!session?.isPersonalDetailsSaved;

  if (isAuthenticated) {
    redirect('/');
  }

  return (
    <div className="flex min-h-screen flex-col">
      <OnBoardHeader />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  );
}
