import type { NextAuthConfig } from 'next-auth';
import Google from 'next-auth/providers/google';

export const authConfig: NextAuthConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  pages: {
    signIn: '/login',
    error: '/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 60 * 24 * 60,
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;

      // Allow static files and API routes
      if (
        nextUrl.pathname.startsWith('/api/') ||
        nextUrl.pathname.startsWith('/_next/') ||
        nextUrl.pathname.startsWith('/favicon') ||
        nextUrl.pathname.includes('.') ||
        nextUrl.pathname === '/manifest.json' ||
        nextUrl.pathname === '/manifest.webmanifest' ||
        nextUrl.pathname === '/robots.txt' ||
        nextUrl.pathname === '/sitemap.xml'
      ) {
        return true;
      }

      // Define onboarding/auth routes that logged-in users should NOT access
      // This prevents logged-in users from seeing login/signup screens
      const onboardingRoutes = [
        '/login',
        '/signup',
        '/forgot-password',
        '/reset-password',
        '/otp',
        '/fill-user-details'
      ];

      const isOnOnboardingRoute = onboardingRoutes.some(route =>
        nextUrl.pathname.startsWith(route)
      );
      console.log(isOnOnboardingRoute, nextUrl.pathname , 'urlTesting....')
      // CUSTOM BEHAVIOR: If user is logged in and trying to access onboarding routes, redirect to forums
      // This prevents logged-in users from accessing login/signup pages
      if (isLoggedIn && isOnOnboardingRoute) {
        return Response.redirect(new URL('/forums', nextUrl));
      }

      // CUSTOM BEHAVIOR: Allow all other routes regardless of authentication status
      // This means non-logged-in users CAN access authenticated screens (unusual but requested)
      // They will see limited content via AuthOverlay components
      return true;
    },
  },
};
